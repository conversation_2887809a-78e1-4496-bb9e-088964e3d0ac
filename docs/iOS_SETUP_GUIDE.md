# 🍎 iOS Flavor Setup Guide

## 📖 Tổng quan

Hướng dẫn chi tiết để thiết lập iOS flavors trong Xcode project.

## 🚀 Bước 1: Ch<PERSON><PERSON> Scripts Tự Động

```bash
# 1. Tạo xcconfig files và schemes
python3 scripts/setup_ios_flavors.py

# 2. Tạo build configurations
python3 scripts/setup_ios_build_configs.py
```

## 🔧 Bước 2: Thiết lập trong Xcode

### 1. Mở Xcode Project
```bash
open ios/Runner.xcworkspace
```

### 2. Thêm Build Configurations

1. **Click vào "Runner" project** (màu xanh, không phải target)
2. **Chọn Runner PROJECT** trong danh sách (không phải Runner TARGET)
3. **Vào tab "Info"**
4. **Trong phần "Configurations"**:

#### Thêm Debug Configurations:
- Click **"+"** → **"Duplicate 'Debug' Configuration"**
- Đổi tên thành: **`Debug-Development`**
- Lặp lại để tạo: **`Debug-Staging`**, **`Debug-Production`**

#### Thêm Release Configurations:
- Click **"+"** → **"Duplicate 'Release' Configuration"**
- Đổi tên thành: **`Release-Development`**
- Lặp lại để tạo: **`Release-Staging`**, **`Release-Production`**

### 3. Assign Configuration Files

Cho mỗi configuration, assign file .xcconfig tương ứng:

| Configuration | Runner Project | Runner Target |
|---------------|----------------|---------------|
| Debug-Development | Debug-Development | Debug-Development |
| Debug-Staging | Debug-Staging | Debug-Staging |
| Debug-Production | Debug-Production | Debug-Production |
| Release-Development | Release-Development | Release-Development |
| Release-Staging | Release-Staging | Release-Staging |
| Release-Production | Release-Production | Release-Production |

### 4. Cập nhật Schemes

1. **Product** → **Scheme** → **Manage Schemes...**
2. **Chọn scheme "Development"**
3. **Click "Edit"**
4. **Trong "Build Configuration"**:
   - **Run**: Debug-Development
   - **Archive**: Release-Development
5. **Lặp lại cho Staging và Production schemes**

## ✅ Bước 3: Test Setup

### Test Build từ Command Line:
```bash
# Development
fvm flutter build ios --flavor development -t lib/main_development.dart --debug --no-codesign

# Staging
fvm flutter build ios --flavor staging -t lib/main_staging.dart --debug --no-codesign

# Production  
fvm flutter build ios --flavor production -t lib/main_production.dart --debug --no-codesign
```

### Test từ Xcode:
1. **Chọn scheme**: Development/Staging/Production
2. **Chọn device/simulator**
3. **Click ▶️ để run**

## 📱 Kết quả mong đợi

| Flavor | Bundle ID | App Name | Icon Badge |
|--------|-----------|----------|------------|
| Development | `com.kienlongbank.sales_app.dev` | Sales App Dev | 🟢 |
| Staging | `com.kienlongbank.sales_app.staging` | Sales App Staging | 🟡 |
| Production | `com.kienlongbank.sales_app` | Sales App | 🔴 |

## 🔍 Troubleshooting

### Lỗi "Build configuration not found"
- **Nguyên nhân**: Chưa tạo build configurations trong Xcode
- **Giải pháp**: Thực hiện lại Bước 2

### Lỗi "Code signing"
- **Nguyên nhân**: Chưa config Development Team
- **Giải pháp**: 
  1. Cập nhật `DEVELOPMENT_TEAM` trong các file .xcconfig
  2. Hoặc config trong Xcode: Target → Signing & Capabilities

### Lỗi "Scheme not found"
- **Nguyên nhân**: Schemes chưa được tạo hoặc shared
- **Giải pháp**: Chạy lại `python3 scripts/setup_ios_flavors.py`

### File .xcconfig không được apply
- **Nguyên nhân**: Configuration files chưa được assign
- **Giải pháp**: Kiểm tra lại Bước 2.3

## 🎯 Best Practices

1. **Luôn build clean** khi switch giữa các flavors:
   ```bash
   fvm flutter clean
   cd ios && rm -rf build/ && cd ..
   ```

2. **Sử dụng đúng scheme** cho từng môi trường

3. **Test trên device thật** để đảm bảo bundle IDs không conflict

4. **Backup Xcode project** trước khi thay đổi configurations

## 🚀 Commands Tổng hợp

```bash
# Setup hoàn chỉnh iOS flavors
python3 scripts/setup_ios_flavors.py
python3 scripts/setup_ios_build_configs.py

# Build tất cả flavors
./scripts/build_flavors_ios.sh

# Clean project
fvm flutter clean && cd ios && rm -rf build/ && cd ..
```

Sau khi hoàn thành các bước trên, iOS flavors sẽ hoạt động hoàn chỉnh! 🎉 