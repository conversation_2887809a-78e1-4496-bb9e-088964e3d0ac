# 🚀 CI/CD Guide - Fastlane & GitLab CI

## 📖 Tổng quan

Hướng dẫn thiết lập và sử dụng CI/CD pipeline cho Sales App với Fastlane và GitLab CI.

## 🏗️ Kiến trúc CI/CD

### Pipeline Stages:
1. **Prepare**: Code analysis, tests, dependencies
2. **Build**: Build APKs cho từng flavor  
3. **Deploy**: Deploy to Google Play Store (2 separate apps)

### Flavors Supported:
- 🟢 **Development**: Tự động build từ `develop` branch
- 🟡 **Staging**: Tự động build từ `staging` branch
- 🔴 **Production**: Tự động build từ `main` branch và tags

## 🔧 Setup Instructions

### 1. Local Setup

```bash
# Clone repository và setup
git clone <repository-url>
cd sales_app

# Run setup script
chmod +x scripts/ci_setup.sh
./scripts/ci_setup.sh
```

### 2. Fastlane Configuration

#### Installation:
```bash
# Install Fastlane
gem install fastlane

# Install plugins (will be done automatically)
cd android
fastlane add_plugin firebase_app_distribution
fastlane add_plugin increment_version_code
```

#### Available Lanes:

##### Build Lanes:
```bash
# Development builds
fastlane flutter_dev              # Build development APK
fastlane build_dev_debug          # Gradle development debug
fastlane build_dev_release        # Gradle development release

# Staging builds  
fastlane flutter_staging          # Build staging APK
fastlane build_staging_debug      # Gradle staging debug
fastlane build_staging_release    # Gradle staging release

# Production builds
fastlane flutter_production       # Build production APK
fastlane build_prod_debug         # Gradle production debug
fastlane build_prod_release       # Gradle production release

# Build all flavors
fastlane flutter_all              # Build all Flutter flavors
fastlane build_all                # Build all Gradle variants
```

##### Deployment Lanes:
```bash
# Google Play Store
fastlane deploy_staging_playstore    # Deploy staging to Play Store (Internal)
fastlane deploy_production_playstore # Deploy production to Play Store (Internal)
```

##### Utility Lanes:
```bash
fastlane version_info             # Show current version and CI info
fastlane test                     # Run tests
```

### 3. GitLab CI Setup

#### Required CI/CD Variables:

Trong GitLab Project → Settings → CI/CD → Variables:

| Variable | Type | Description | Required |
|----------|------|-------------|----------|
| `GOOGLE_PLAY_JSON_KEY_STAGING` | File | Service account key for staging app | For staging deployment |
| `GOOGLE_PLAY_JSON_KEY_PRODUCTION` | File | Service account key for production app | For production deployment |
| `ANDROID_KEYSTORE_FILE` | File | Release keystore file | For signing APK/AAB |
| `ANDROID_KEYSTORE_PASSWORD` | Variable (masked) | Keystore password | For signing |
| `ANDROID_KEY_ALIAS` | Variable | Key alias | For signing |
| `ANDROID_KEY_PASSWORD` | Variable (masked) | Key password | For signing |

#### Branch Strategy:

| Branch | Auto Build | Deploy | Notes |
|--------|------------|--------|--------|
| `develop` | ✅ Development | ❌ No deployment | Daily development |
| `feature/*` | ✅ Development | ❌ No deployment | Feature branches |
| `staging` | ✅ Staging | Manual to Play Store | UAT/Testing |
| `main` | ✅ Production | Manual to Play Store | Production releases |
| `tags` | ✅ Production | Manual to Play Store | Version releases |

### 4. Android Signing Configuration

#### Create Keystore:
```bash
# Development keystore (auto-generated)
keytool -genkey -v -keystore android/app/debug.keystore -storepass android -alias androiddebugkey -keypass android -keyalg RSA -keysize 2048 -validity 10000

# Production keystore
keytool -genkey -v -keystore android/app/keystore/release.keystore -alias release -keyalg RSA -keysize 2048 -validity 10000
```

#### Configure key.properties:
```properties
# android/key.properties
storePassword=your_store_password
keyPassword=your_key_password  
keyAlias=release
storeFile=keystore/release.keystore
```

## 🚀 Usage Examples

### Local Development:

```bash
# Build development locally
cd android
fastlane flutter_dev

# Build all flavors
fastlane flutter_all

# Deploy staging to Play Store (requires proper setup)
fastlane deploy_staging_playstore
```

### GitLab CI Pipeline:

#### Automatic Builds:
- **Push to `develop`** → Build development APK
- **Push to `staging`** → Build staging APK  
- **Push to `main`** → Build production APK

#### Manual Deployments:
1. Go to GitLab Project → Pipelines
2. Find your pipeline
3. Click manual deployment job:
   - `deploy_staging` 
   - `deploy_production`

## 📱 Build Outputs

### APK Locations:
```
build/app/outputs/flutter-apk/
├── app-development-release.apk     # Development builds
├── app-staging-release.apk         # Staging builds  
└── app-production-release.apk      # Production builds
```

### App Information:
| Flavor | Package Name | App Name | Signing |
|--------|--------------|----------|---------|
| Development | `com.kienlongbank.sales_app.dev` | Sales App Dev | Debug |
| Staging | `com.kienlongbank.sales_app.staging` | Sales App Staging | Release/Staging |
| Production | `com.kienlongbank.sales_app` | Sales App | Release |

## 🔍 Troubleshooting

### Common Issues:

#### 1. Fastlane Build Failures:
```bash
# Clean and retry
fvm flutter clean
cd android
fastlane flutter_dev
```

#### 2. Signing Issues:
- Check `android/key.properties` configuration
- Verify keystore file exists
- Check GitLab CI variables

#### 3. Google Play Store Upload Failures:
- Verify service account permissions
- Check package name matches app configuration
- Ensure version code is incremented
- Validate APK/AAB file integrity

### Debug Commands:

```bash
# Check Fastlane configuration
cd android
fastlane lanes

# Test Flutter configuration
fvm flutter doctor -v

# Test build locally
fvm flutter build apk --flavor development -t lib/main_development.dart
```

## 📊 Pipeline Performance

### Optimization Tips:

1. **Use GitLab Runner with Docker cache**
2. **Cache Gradle dependencies**
3. **Use `when: manual` for expensive operations**
4. **Parallel builds for different flavors**

### Expected Build Times:
- **Analysis & Tests**: 2-3 minutes
- **Development Build**: 5-8 minutes
- **Staging Build**: 6-10 minutes  
- **Production Build**: 8-12 minutes

## 🎯 Best Practices

### Development Workflow:
1. **Feature development** → Push to `feature/*` → Auto build dev
2. **Ready for testing** → Merge to `staging` → Manual deploy to Play Store
3. **Ready for release** → Merge to `main` → Manual deploy to Play Store

### Release Process:
1. Create release branch from `develop`
2. Test on `staging` environment
3. Create tag and merge to `main`
4. Deploy to production

### Security:
- ✅ Keystore files stored as GitLab CI File variables
- ✅ Passwords stored as masked variables
- ✅ No secrets in repository
- ✅ Signed releases only

Ready to deploy! 🚀 