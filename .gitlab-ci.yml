# GitLab CI/CD Configuration for Sales App
# Android Build with Multiple Flavors

image: cirrusci/flutter:3.24.5

variables:
  ANDROID_COMPILE_SDK: "34"
  ANDROID_BUILD_TOOLS: "34.0.0"
  ANDROID_SDK_TOOLS: "9477386"
  APP_VERSION: "1.0.0"  # Update this when releasing new version

# Cache configuration
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - .gradle/wrapper
    - .gradle/caches
    - android/.gradle/wrapper
    - android/.gradle/caches
    - $HOME/.pub-cache

# Stages definition
stages:
  - prepare
  - test
  - build
  - deploy

# Before script - setup environment
before_script:
  # Install system dependencies
  - apt-get update -qq && apt-get install -y -qq git curl unzip build-essential libstdc++6
  
  # Setup FVM (Flutter Version Management)
  - dart pub global activate fvm
  - export PATH="$PATH":"$HOME/.pub-cache/bin"
  - fvm install
  - fvm use --force
  
  # Get Flutter dependencies
  - fvm flutter doctor -v
  - fvm flutter pub get
  
  # Setup Fastlane
  - gem install fastlane -NV
  
  # Setup Android SDK if needed
  - if [ ! -d "$ANDROID_HOME" ]; then
      export ANDROID_HOME=$HOME/android-sdk-linux;
      mkdir -p $ANDROID_HOME;
    fi

# ===================
# PREPARE STAGE
# ===================

flutter_analyze:
  stage: prepare
  script:
    - fvm flutter analyze
  allow_failure: false
  only:
    - merge_requests
    - main
    - develop

flutter_test:
  stage: prepare
  script:
    - fvm flutter test
  coverage: '/lines......: \d+\.\d+\%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura.xml
  only:
    - merge_requests
    - main
    - develop

# ===================
# BUILD STAGE - DEVELOPMENT
# ===================

build_development_debug:
  stage: build
  script:
    - cd android
    - fastlane flutter_dev
  artifacts:
    paths:
      - build/app/outputs/flutter-apk/app-development-release.apk
    expire_in: 1 week
  only:
    - develop
    - /^feature\/.*$/

build_development_release:
  stage: build
  script:
    - cd android
    - fastlane flutter_dev
  artifacts:
    paths:
      - build/app/outputs/flutter-apk/app-development-release.apk
    expire_in: 1 month
  only:
    - develop

# ===================
# BUILD STAGE - STAGING
# ===================

build_staging_debug:
  stage: build
  script:
    - cd android
    - fastlane flutter_staging
  artifacts:
    paths:
      - build/app/outputs/flutter-apk/app-staging-release.apk
    expire_in: 1 week
  only:
    - /^release\/.*$/
    - staging

build_staging_release:
  stage: build
  script:
    - cd android
    - fastlane flutter_staging_aab
  artifacts:
    paths:
      - build/app/outputs/bundle/stagingRelease/app-staging-release.aab
      - build/app/outputs/flutter-apk/app-staging-release.apk
    expire_in: 1 month
  only:
    - staging

# ===================
# BUILD STAGE - PRODUCTION
# ===================

build_production_release:
  stage: build
  script:
    - cd android
    - fastlane flutter_production_aab
  artifacts:
    paths:
      - build/app/outputs/bundle/productionRelease/app-production-release.aab
      - build/app/outputs/flutter-apk/app-production-release.apk
    expire_in: 3 months
  only:
    - main
    - tags

# ===================
# BUILD ALL FLAVORS (Manual)
# ===================

build_all_flavors:
  stage: build
  script:
    - cd android
    - fastlane flutter_all
  artifacts:
    paths:
      - build/app/outputs/flutter-apk/
    expire_in: 1 week
  when: manual
  only:
    - main
    - develop
    - staging

# ===================
# DEPLOY STAGE
# ===================

deploy_staging:
  stage: deploy
  dependencies:
    - build_staging_release
  script:
    - cd android
    - fastlane deploy_staging_playstore
  environment:
    name: staging-playstore
    url: https://play.google.com/console/
  only:
    - staging
  when: manual

deploy_production:
  stage: deploy
  dependencies:
    - build_production_release
  script:
    - cd android
    - fastlane deploy_production_playstore
  environment:
    name: production-playstore
    url: https://play.google.com/console/
  only:
    - main
    - tags
  when: manual

promote_staging_to_external:
  stage: deploy
  script:
    - cd android
    - fastlane promote_staging_to_external
  environment:
    name: staging-external-test
    url: https://play.google.com/console/
  only:
    - staging
  when: manual

promote_production_to_live:
  stage: deploy
  script:
    - cd android
    - fastlane promote_production_to_production
  environment:
    name: production-live
    url: https://play.google.com/console/
  only:
    - main
    - tags
  when: manual

# ===================
# UTILITY JOBS
# ===================

version_bump:
  stage: prepare
  script:
    - cd android
    - fastlane increment_version
    - fastlane version_info
  only:
    - main
  when: manual

# ===================
# CLEANUP JOBS
# ===================

cleanup_artifacts:
  stage: deploy
  script:
    - echo "Cleaning up old artifacts..."
    - rm -rf build/
  when: always
  dependencies: [] 