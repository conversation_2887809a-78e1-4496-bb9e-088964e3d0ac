# app_identifier("[[APP_IDENTIFIER]]") # The bundle identifier of your app
# apple_id("[[APPLE_ID]]") # Your Apple email address

# For more information about the Appfile, see:
#     https://docs.fastlane.tools/advanced/#appfile

# Android specific configuration
package_name("com.kienlongbank.sales_app") # Base package name

# Development flavor configuration
for_lane :flutter_dev do
  package_name("com.kienlongbank.sales_app.dev")
end

for_lane :build_dev_debug do
  package_name("com.kienlongbank.sales_app.dev")
end

for_lane :build_dev_release do
  package_name("com.kienlongbank.sales_app.dev")
end

for_lane :deploy_dev_firebase do
  package_name("com.kienlongbank.sales_app.dev")
end

# Staging flavor configuration
for_lane :flutter_staging do
  package_name("com.kienlongbank.sales_app.staging")
end

for_lane :build_staging_debug do
  package_name("com.kienlongbank.sales_app.staging")
end

for_lane :build_staging_release do
  package_name("com.kienlongbank.sales_app.staging")
end

for_lane :deploy_staging_firebase do
  package_name("com.kienlongbank.sales_app.staging")
end

# Production flavor configuration (default)
for_lane :flutter_production do
  package_name("com.kienlongbank.sales_app")
end

for_lane :build_prod_debug do
  package_name("com.kienlongbank.sales_app")
end

for_lane :build_prod_release do
  package_name("com.kienlongbank.sales_app")
end

for_lane :deploy_production_playstore do
  package_name("com.kienlongbank.sales_app")
end 