fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## Android

### android test

```sh
[bundle exec] fastlane android test
```

Runs all the tests

### android beta

```sh
[bundle exec] fastlane android beta
```

Submit a new Beta Build to Crashlytics Beta

### android deploy

```sh
[bundle exec] fastlane android deploy
```

Deploy a new version to the Google Play

### android build_dev_debug

```sh
[bundle exec] fastlane android build_dev_debug
```

Build Development Debug APK

### android build_dev_release

```sh
[bundle exec] fastlane android build_dev_release
```

Build Development Release APK

### android build_staging_debug

```sh
[bundle exec] fastlane android build_staging_debug
```

Build Staging Debug APK

### android build_staging_release

```sh
[bundle exec] fastlane android build_staging_release
```

Build Staging Release APK

### android build_prod_debug

```sh
[bundle exec] fastlane android build_prod_debug
```

Build Production Debug APK

### android build_prod_release

```sh
[bundle exec] fastlane android build_prod_release
```

Build Production Release APK

### android build_all

```sh
[bundle exec] fastlane android build_all
```

Build all flavors and variants

### android flutter_dev

```sh
[bundle exec] fastlane android flutter_dev
```

Build Flutter Development APK

### android flutter_staging

```sh
[bundle exec] fastlane android flutter_staging
```

Build Flutter Staging APK

### android flutter_production

```sh
[bundle exec] fastlane android flutter_production
```

Build Flutter Production APK

### android flutter_all

```sh
[bundle exec] fastlane android flutter_all
```

Build all Flutter flavors

### android flutter_staging_aab

```sh
[bundle exec] fastlane android flutter_staging_aab
```

Build Flutter Staging AAB for Play Store

### android flutter_production_aab

```sh
[bundle exec] fastlane android flutter_production_aab
```

Build Flutter Production AAB for Play Store

### android deploy_staging_playstore

```sh
[bundle exec] fastlane android deploy_staging_playstore
```

Deploy Staging to Google Play Store (Internal Test)

### android deploy_production_playstore

```sh
[bundle exec] fastlane android deploy_production_playstore
```

Deploy Production to Google Play Store (Internal Test)

### android promote_staging_to_external

```sh
[bundle exec] fastlane android promote_staging_to_external
```

Promote Staging to External Test

### android promote_production_to_production

```sh
[bundle exec] fastlane android promote_production_to_production
```

Promote Production to Production Track

### android version_info

```sh
[bundle exec] fastlane android version_info
```

Show current version info

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
