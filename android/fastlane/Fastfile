# This file contains the fastlane configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#     https://docs.fastlane.tools/plugins/available-plugins

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  # Variables
  GRADLE_FILE = "app/build.gradle.kts"
  
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleDevelopmentRelease")
    crashlytics
  
    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleProductionRelease")
    upload_to_play_store
  end

  # ===================
  # DEVELOPMENT LANES
  # ===================
  
  desc "Build Development Debug APK"
  lane :build_dev_debug do
    gradle(
      task: "clean",
      flavor: "development",
      build_type: "Debug"
    )
    gradle(
      task: "assemble",
      flavor: "development", 
      build_type: "Debug"
    )
  end

  desc "Build Development Release APK"
  lane :build_dev_release do
    gradle(
      task: "clean",
      flavor: "development",
      build_type: "Release"
    )
    gradle(
      task: "assemble",
      flavor: "development",
      build_type: "Release"
    )
  end

  # ===================
  # STAGING LANES
  # ===================
  
  desc "Build Staging Debug APK"
  lane :build_staging_debug do
    gradle(
      task: "clean",
      flavor: "staging",
      build_type: "Debug"
    )
    gradle(
      task: "assemble",
      flavor: "staging",
      build_type: "Debug"
    )
  end

  desc "Build Staging Release APK"
  lane :build_staging_release do
    gradle(
      task: "clean",
      flavor: "staging",
      build_type: "Release"
    )
    gradle(
      task: "assemble",
      flavor: "staging",
      build_type: "Release"
    )
  end

  # ===================
  # PRODUCTION LANES
  # ===================
  
  desc "Build Production Debug APK"
  lane :build_prod_debug do
    gradle(
      task: "clean",
      flavor: "production",
      build_type: "Debug"
    )
    gradle(
      task: "assemble",
      flavor: "production",
      build_type: "Debug"
    )
  end

  desc "Build Production Release APK"
  lane :build_prod_release do
    gradle(
      task: "clean",
      flavor: "production",
      build_type: "Release"
    )
    gradle(
      task: "assemble",
      flavor: "production",
      build_type: "Release"
    )
  end

  # ===================
  # BUILD ALL FLAVORS
  # ===================
  
  desc "Build all flavors and variants"
  lane :build_all do
    gradle(task: "clean")
    
    # Build all development variants
    gradle(task: "assembleDevelopmentDebug")
    gradle(task: "assembleDevelopmentRelease")
    
    # Build all staging variants  
    gradle(task: "assembleStagingDebug")
    gradle(task: "assembleStagingRelease")
    
    # Build all production variants
    gradle(task: "assembleProductionDebug") 
    gradle(task: "assembleProductionRelease")
    
    UI.success("🎉 All flavors built successfully!")
  end

  # ===================
  # FLUTTER SPECIFIC LANES
  # ===================
  
  desc "Build Flutter Development APK"
  lane :flutter_dev do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    Dir.chdir("..") do
      sh("fvm flutter build apk --flavor development -t lib/main_development.dart --release --build-number=#{build_number} --build-name=#{build_name}")
    end
    UI.success("✅ Flutter Development APK built!")
  end

  desc "Build Flutter Staging APK" 
  lane :flutter_staging do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    Dir.chdir("..") do
      sh("fvm flutter build apk --flavor staging -t lib/main_staging.dart --release --build-number=#{build_number} --build-name=#{build_name}")
    end
    UI.success("✅ Flutter Staging APK built!")
  end

  desc "Build Flutter Production APK"
  lane :flutter_production do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    Dir.chdir("..") do
      sh("fvm flutter build apk --flavor production -t lib/main_production.dart --release --build-number=#{build_number} --build-name=#{build_name}")
    end
    UI.success("✅ Flutter Production APK built!")
  end

  desc "Build all Flutter flavors"
  lane :flutter_all do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    Dir.chdir("..") do
      sh("fvm flutter clean")
      
      # Build Development
      sh("fvm flutter build apk --flavor development -t lib/main_development.dart --release --build-number=#{build_number} --build-name=#{build_name}")
      
      # Build Staging
      sh("fvm flutter build apk --flavor staging -t lib/main_staging.dart --release --build-number=#{build_number} --build-name=#{build_name}")
      
      # Build Production  
      sh("fvm flutter build apk --flavor production -t lib/main_production.dart --release --build-number=#{build_number} --build-name=#{build_name}")
    end
    UI.success("🚀 All Flutter flavors built successfully!")
  end

  # ===================
  # FLUTTER AAB (BUNDLE) LANES
  # ===================
  
  desc "Build Flutter Staging AAB for Play Store"
  lane :flutter_staging_aab do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    Dir.chdir("..") do
      sh("fvm flutter build appbundle --flavor staging -t lib/main_staging.dart --release --build-number=#{build_number} --build-name=#{build_name}")
    end
    UI.success("✅ Flutter Staging AAB built!")
  end

  desc "Build Flutter Production AAB for Play Store"
  lane :flutter_production_aab do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    Dir.chdir("..") do
      sh("fvm flutter build appbundle --flavor production -t lib/main_production.dart --release --build-number=#{build_number} --build-name=#{build_name}")
    end
    UI.success("✅ Flutter Production AAB built!")
  end

  # ===================
  # DEPLOYMENT LANES
  # ===================
  
  desc "Deploy Staging to Google Play Store (Internal Test)"
  lane :deploy_staging_playstore do
    flutter_staging_aab
    upload_to_play_store(
      track: "internal",
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_STAGING"],
      package_name: "com.kienlongbank.sales_app.staging",
      aab: "../build/app/outputs/bundle/stagingRelease/app-staging-release.aab",
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  desc "Deploy Production to Google Play Store (Internal Test)"
  lane :deploy_production_playstore do
    flutter_production_aab
    upload_to_play_store(
      track: "internal",
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_PRODUCTION"],
      package_name: "com.kienlongbank.sales_app",
      aab: "../build/app/outputs/bundle/productionRelease/app-production-release.aab",
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  desc "Promote Staging to External Test"
  lane :promote_staging_to_external do
    upload_to_play_store(
      track: "internal",
      track_promote_to: "alpha",
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_STAGING"],
      package_name: "com.kienlongbank.sales_app.staging",
      skip_upload_apk: true,
      skip_upload_aab: true,
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  desc "Promote Production to Production Track"
  lane :promote_production_to_production do
    upload_to_play_store(
      track: "internal",
      track_promote_to: "production",
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_PRODUCTION"],
      package_name: "com.kienlongbank.sales_app",
      skip_upload_apk: true,
      skip_upload_aab: true,
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  # ===================
  # UTILITY LANES
  # ===================
  
  desc "Show current version info"
  lane :version_info do
    # Show current version from environment variables
    ci_job_id = ENV["CI_JOB_ID"] || "Local Build"
    app_version = ENV["APP_VERSION"] || "1.0.0"
    ci_pipeline_id = ENV["CI_PIPELINE_ID"] || "N/A"
    git_commit = ENV["CI_COMMIT_SHORT_SHA"] || "N/A"
    
    UI.message("🔢 Build Number (Version Code): #{ci_job_id}")
    UI.message("📱 Build Name (Version Name): #{app_version}")
    UI.message("🚀 Pipeline ID: #{ci_pipeline_id}")
    UI.message("📝 Git Commit: #{git_commit}")
  end

  # ===================
  # ERROR HANDLING
  # ===================
  
  error do |lane, exception|
    UI.error("❌ Lane '#{lane}' failed with error: #{exception}")
    # Có thể thêm notification hoặc cleanup ở đây
  end
end 