# Environment Variables for CI/CD
# Copy this file to .env and fill in your actual values

# ===================
# GOOGLE PLAY STORE - STAGING APP
# ===================
GOOGLE_PLAY_JSON_KEY_STAGING=path/to/google-play-staging-service-account.json
GOOGLE_PLAY_PACKAGE_NAME_STAGING=com.kienlongbank.sales_app.staging

# ===================
# GOOGLE PLAY STORE - PRODUCTION APP
# ===================
GOOGLE_PLAY_JSON_KEY_PRODUCTION=path/to/google-play-production-service-account.json
GOOGLE_PLAY_PACKAGE_NAME_PRODUCTION=com.kienlongbank.sales_app

# ===================
# ANDROID SIGNING
# ===================
# For production builds
ANDROID_KEYSTORE_PATH=path/to/release.keystore
ANDROID_KEYSTORE_PASSWORD=your_keystore_password
ANDROID_KEY_ALIAS=your_key_alias
ANDROID_KEY_PASSWORD=your_key_password

# ===================
# GITLAB CI VARIABLES
# ===================
# Set these in GitLab Project Settings > CI/CD > Variables
# - GOOGLE_PLAY_JSON_KEY_STAGING (file type)
# - GOOGLE_PLAY_JSON_KEY_PRODUCTION (file type)
# - ANDROID_KEYSTORE_FILE (file type)
# - ANDROID_KEYSTORE_PASSWORD (masked)
# - ANDROID_KEY_ALIAS
# - ANDROID_KEY_PASSWORD (masked)

# ===================
# NOTIFICATION
# ===================
SLACK_WEBHOOK_URL=your_slack_webhook_url
DISCORD_WEBHOOK_URL=your_discord_webhook_url

# ===================
# VERSIONING (Auto-managed by CI)
# ===================
# CI_JOB_ID is automatically set by GitLab CI and used as version code
# APP_VERSION can be set manually for version name (e.g., 1.0.0, 1.1.0)
APP_VERSION=1.0.0 