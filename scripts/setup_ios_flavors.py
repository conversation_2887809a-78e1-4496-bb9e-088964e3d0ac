#!/usr/bin/env python3
"""
iOS Flavor Setup Script
Tự động thiết lập Xcode schemes và configurations cho các flavors
"""

import os
import json
import sys
from pathlib import Path

def create_xcode_scheme(flavor_name, target_name, configuration_name):
    """Tạo Xcode scheme cho flavor"""
    scheme_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1510"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "97C146ED1CF9000F007C117D"
               BuildableName = "{target_name}.app"
               BlueprintName = "Runner"
               ReferencedContainer = "container:Runner.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "{configuration_name}"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES">
      <Testables>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "331C8080294A63A400263BE5"
               BuildableName = "RunnerTests.xctest"
               BlueprintName = "RunnerTests"
               ReferencedContainer = "container:Runner.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "{configuration_name}"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "97C146ED1CF9000F007C117D"
            BuildableName = "{target_name}.app"
            BlueprintName = "Runner"
            ReferencedContainer = "container:Runner.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "{configuration_name}"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "97C146ED1CF9000F007C117D"
            BuildableName = "{target_name}.app"
            BlueprintName = "Runner"
            ReferencedContainer = "container:Runner.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "{configuration_name}">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "{configuration_name}"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>"""
    
    return scheme_content

def setup_ios_flavors():
    """Thiết lập iOS flavors"""
    print("🍎 Setting up iOS Flavors...")
    
    # Đường dẫn đến project
    ios_dir = Path("ios")
    scheme_dir = ios_dir / "Runner.xcodeproj" / "xcshareddata" / "xcschemes"
    
    # Tạo thư mục schemes nếu chưa có
    scheme_dir.mkdir(parents=True, exist_ok=True)
    
    # Flavors configuration
    flavors = {
        "Development": {
            "scheme_name": "Development",
            "target_name": "Sales App Dev", 
            "configuration": "Debug-Development"
        },
        "Staging": {
            "scheme_name": "Staging", 
            "target_name": "Sales App Staging",
            "configuration": "Debug-Staging"
        },
        "Production": {
            "scheme_name": "Production",
            "target_name": "Sales App",
            "configuration": "Release-Production"
        }
    }
    
    # Tạo schemes cho từng flavor
    for flavor_name, config in flavors.items():
        scheme_file = scheme_dir / f"{config['scheme_name']}.xcscheme"
        scheme_content = create_xcode_scheme(
            config['scheme_name'], 
            config['target_name'], 
            config['configuration']
        )
        
        with open(scheme_file, 'w') as f:
            f.write(scheme_content)
        
        print(f"✅ Created scheme: {config['scheme_name']}")
    
    print("\n🎯 iOS Flavors setup completed!")
    print("📝 Next steps:")
    print("1. Mở Xcode project: ios/Runner.xcworkspace")
    print("2. Chọn scheme phù hợp từ dropdown (Development/Staging/Production)")  
    print("3. Build và test từng flavor")
    print("\n🚀 Ready to use iOS flavors!")

if __name__ == "__main__":
    setup_ios_flavors() 