#!/usr/bin/env python3
"""
iOS Build Configurations Setup Script
Tự động thiết lập build configurations trong Xcode project cho flavors
"""

import os
import shutil
from pathlib import Path

def setup_ios_build_configurations():
    """Thiết lập iOS build configurations"""
    print("🍎 Setting up iOS Build Configurations...")
    
    # Đường dẫn đến project
    ios_dir = Path("ios")
    flutter_dir = ios_dir / "Flutter"
    
    # Kiểm tra các file xcconfig đã tồn tại
    development_config = flutter_dir / "Development.xcconfig"
    staging_config = flutter_dir / "Staging.xcconfig" 
    production_config = flutter_dir / "Production.xcconfig"
    
    if not all([development_config.exists(), staging_config.exists(), production_config.exists()]):
        print("❌ Các file xcconfig chưa được tạo. Vui lòng chạy setup_ios_flavors.py trước.")
        return
    
    # Tạo build configurations bằng cách copy từ Debug và Release
    debug_config = flutter_dir / "Debug.xcconfig"
    release_config = flutter_dir / "Release.xcconfig"
    
    # Tạo Debug configurations cho từng flavor
    debug_dev = flutter_dir / "Debug-Development.xcconfig"
    debug_staging = flutter_dir / "Debug-Staging.xcconfig"
    debug_prod = flutter_dir / "Debug-Production.xcconfig"
    
    # Tạo Release configurations cho từng flavor
    release_dev = flutter_dir / "Release-Development.xcconfig"
    release_staging = flutter_dir / "Release-Staging.xcconfig"
    release_prod = flutter_dir / "Release-Production.xcconfig"
    
    # Copy và customize Debug configs
    if debug_config.exists():
        # Debug-Development
        with open(debug_config, 'r') as f:
            debug_content = f.read()
        with open(debug_dev, 'w') as f:
            f.write(debug_content + '\n#include "Development.xcconfig"\n')
        print("✅ Created Debug-Development.xcconfig")
        
        # Debug-Staging
        with open(debug_staging, 'w') as f:
            f.write(debug_content + '\n#include "Staging.xcconfig"\n')
        print("✅ Created Debug-Staging.xcconfig")
        
        # Debug-Production
        with open(debug_prod, 'w') as f:
            f.write(debug_content + '\n#include "Production.xcconfig"\n')
        print("✅ Created Debug-Production.xcconfig")
    
    # Copy và customize Release configs
    if release_config.exists():
        # Release-Development
        with open(release_config, 'r') as f:
            release_content = f.read()
        with open(release_dev, 'w') as f:
            f.write(release_content + '\n#include "Development.xcconfig"\n')
        print("✅ Created Release-Development.xcconfig")
        
        # Release-Staging
        with open(release_staging, 'w') as f:
            f.write(release_content + '\n#include "Staging.xcconfig"\n')
        print("✅ Created Release-Staging.xcconfig")
        
        # Release-Production
        with open(release_prod, 'w') as f:
            f.write(release_content + '\n#include "Production.xcconfig"\n')
        print("✅ Created Release-Production.xcconfig")
    
    print("\n🎯 iOS Build Configurations setup completed!")
    print("📝 Next steps:")
    print("1. Mở Xcode: open ios/Runner.xcworkspace")
    print("2. Click vào 'Runner' project (không phải target)")
    print("3. Trong 'Configurations', thêm các configurations:")
    print("   - Debug-Development")
    print("   - Debug-Staging")  
    print("   - Debug-Production")
    print("   - Release-Development")
    print("   - Release-Staging")
    print("   - Release-Production")
    print("4. Assign các .xcconfig files tương ứng")
    print("\n🚀 Sau đó có thể build iOS flavors!")

if __name__ == "__main__":
    setup_ios_build_configurations() 