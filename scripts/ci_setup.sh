#!/bin/bash

# CI/CD Setup Script
# Thiết lập môi trường CI/CD cho Sales App

set -e  # Exit on any error

echo "🚀 Setting up CI/CD environment for Sales App..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# ===================
# FUNCTIONS
# ===================

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 không được tìm thấy. Vui lòng cài đặt $1."
        return 1
    else
        log_info "$1 đã được cài đặt."
        return 0
    fi
}

# ===================
# CHECK DEPENDENCIES
# ===================

log_info "Kiểm tra dependencies..."

# Check Flutter/FVM
if check_command "fvm"; then
    log_info "FVM version: $(fvm --version)"
else
    log_warn "FVM chưa được cài đặt. Cài đặt bằng: dart pub global activate fvm"
fi

# Check Fastlane
if check_command "fastlane"; then
    log_info "Fastlane version: $(fastlane --version)"
else
    log_warn "Fastlane chưa được cài đặt. Cài đặt bằng: gem install fastlane"
fi

# Check Docker (for GitLab Runner)
if check_command "docker"; then
    log_info "Docker version: $(docker --version)"
else
    log_warn "Docker chưa được cài đặt."
fi

# ===================
# FLUTTER SETUP
# ===================

log_info "Thiết lập Flutter environment..."

# Install FVM Flutter version
if command -v fvm &> /dev/null; then
    log_info "Installing Flutter version from .fvm/fvm_config.json..."
    fvm install
    fvm use --force
    
    log_info "Getting Flutter dependencies..."
    fvm flutter pub get
    
    log_info "Running Flutter doctor..."
    fvm flutter doctor -v
else
    log_warn "Skipping FVM setup - not available"
fi

# ===================
# FASTLANE SETUP
# ===================

log_info "Thiết lập Fastlane..."

cd android

# Initialize Fastlane if not exists
if [ ! -f "fastlane/Fastfile" ]; then
    log_warn "Fastfile không tồn tại. Creating basic structure..."
    mkdir -p fastlane
    fastlane init
else
    log_info "Fastfile đã tồn tại."
fi

# Install Fastlane plugins
log_info "Installing Fastlane plugins..."
fastlane add_plugin firebase_app_distribution || log_warn "Firebase plugin already installed"
fastlane add_plugin increment_version_code || log_warn "Version increment plugin already installed"

cd ..

# ===================
# KEYSTORE SETUP
# ===================

log_info "Kiểm tra keystore configuration..."

KEYSTORE_DIR="android/app/keystore"
mkdir -p $KEYSTORE_DIR

# Create key.properties template if not exists
if [ ! -f "android/key.properties" ]; then
    log_info "Creating key.properties template..."
    cat > android/key.properties << EOF
# Keystore configuration for release builds
# This file should be excluded from version control

# Production keystore
storePassword=your_store_password
keyPassword=your_key_password
keyAlias=your_key_alias
storeFile=keystore/release.keystore

# Staging keystore (optional)
stagingStorePassword=your_staging_store_password
stagingKeyPassword=your_staging_key_password
stagingKeyAlias=your_staging_key_alias
stagingStoreFile=keystore/staging.keystore
EOF
    log_warn "Please update android/key.properties with your actual keystore information"
else
    log_info "key.properties already exists"
fi

# ===================
# GITLAB CI SETUP
# ===================

log_info "Thiết lập GitLab CI..."

if [ ! -f ".gitlab-ci.yml" ]; then
    log_error ".gitlab-ci.yml không tồn tại!"
else
    log_info ".gitlab-ci.yml đã được cấu hình."
fi

# ===================
# ENVIRONMENT VARIABLES
# ===================

log_info "Kiểm tra environment variables..."

if [ ! -f "ci/env.example" ]; then
    log_error "ci/env.example không tồn tại!"
else
    log_info "Environment variables template đã sẵn sàng trong ci/env.example"
fi

# ===================
# BUILD TEST
# ===================

log_info "Testing build configuration..."

# Test Flutter analyze
if command -v fvm &> /dev/null; then
    log_info "Running Flutter analyze..."
    fvm flutter analyze || log_warn "Flutter analyze có warnings"
    
    # Test build development
    log_info "Testing development build..."
    fvm flutter build apk --flavor development -t lib/main_development.dart --debug || log_error "Development build failed"
else
    log_warn "Skipping Flutter tests - FVM not available"
fi

# ===================
# COMPLETION
# ===================

log_info "✅ CI/CD setup completed!"
echo ""
log_info "📝 Next steps:"
echo "1. Cập nhật android/key.properties với keystore thực tế"
echo "2. Thiết lập GitLab CI/CD variables:"
echo "   - FIREBASE_TOKEN"
echo "   - GOOGLE_PLAY_JSON_KEY"
echo "   - ANDROID_KEYSTORE_FILE"
echo "   - ANDROID_KEYSTORE_PASSWORD"
echo "   - ANDROID_KEY_ALIAS"
echo "   - ANDROID_KEY_PASSWORD"
echo "3. Test CI/CD pipeline với commit đầu tiên"
echo ""
log_info "🚀 Ready for CI/CD deployment!" 