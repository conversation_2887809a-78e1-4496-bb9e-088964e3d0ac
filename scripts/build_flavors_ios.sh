#!/bin/bash

# Build script cho iOS flavors của Sales App
echo "🍎 Building Sales App iOS flavors..."

# Kiểm tra xem có Xcode không
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ Xcode không được tìm thấy. Vui lòng cài đặt Xcode."
    exit 1
fi

# Đường dẫn đến thư mục iOS
cd ios

echo "📱 Building Development flavor..."
fvm flutter build ios --flavor development -t ../lib/main_development.dart --release --no-codesign

echo "📱 Building Staging flavor..."
fvm flutter build ios --flavor staging -t ../lib/main_staging.dart --release --no-codesign

echo "📱 Building Production flavor..."
fvm flutter build ios --flavor production -t ../lib/main_production.dart --release --no-codesign

echo "✅ All iOS flavors built successfully!"
echo "📁 Build outputs are located in: ios/build/ios/iphoneos/"

# Quay lại thư mục gốc
cd .. 